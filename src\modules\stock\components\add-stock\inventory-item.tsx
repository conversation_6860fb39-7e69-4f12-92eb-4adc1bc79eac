import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { FormField } from "@/shared/components/ui/form";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Separator } from "@/shared/components/ui/separator";
import { BarcodeInput } from "@/shared/components/utils/barcode-input";
import { Box, HelpCircle, Package2, Trash2, Calculator } from "lucide-react";
import React, { useState } from "react";
import { Controller, UseFormReturn, useWatch, type FieldError } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { useCalculatedQuantity } from "../../hooks/quantity/calculate.hook";
import { ICreateStock } from "../../validators/create-stock.validator";
import { PricingCalculator } from "./pricing-calculator";
import { CategorySelect } from "@/modules/product/components/category/category-select";

interface InventoryItemProps {
	index: number;
	methodsForm: UseFormReturn<ICreateStock>;
	removeItem: () => void;
	isExistingIdProduct: boolean;
	isExistingIdPackage: boolean;
}

export const InventoryItem: React.FC<InventoryItemProps> = ({
	index,
	methodsForm,
	removeItem,
	isExistingIdProduct = false,
	isExistingIdPackage = false,
}) => {
	const [showPricingCalculator, setShowPricingCalculator] = useState(false);
	const readonlyClass = "bg-gray-100 cursor-not-allowed text-gray-500";

	const quantityPerPackageValue = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.product.package.quantityPerPackage`,
	});

	const packageQuantity = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.packageQuantity`,
		defaultValue: undefined,
	});

	const currentCostPrice = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.product.costPrice`,
	});

	const parseMonetaryValue = (value: string | number | undefined): number => {
		if (typeof value === "number") return value;
		if (!value) return 0;

		const cleanValue = value
			.toString()
			.replace(/R\$\s?/, "")
			.replace(/\./g, "")
			.replace(",", ".");

		const numericValue = parseFloat(cleanValue);
		return isNaN(numericValue) ? 0 : numericValue;
	};

	const fieldName = `inventories.${index}.stockMovement.quantity` as const;
	const { calculatedQuantity, handleQuantityChange } = useCalculatedQuantity({
		quantityPerPackageValue,
		packageQuantity,
		setValue: methodsForm.setValue,
		fieldName,
	});

	const getError = (path: string): FieldError | undefined => {
		const error = path.split(".").reduce(
			(acc: unknown, key: string) => {
				if (acc && typeof acc === "object" && key in acc) {
					return (acc as Record<string, unknown>)[key];
				}
				return undefined;
			},
			methodsForm.formState.errors as Record<string, unknown> | undefined
		);
		return error as FieldError | undefined;
	};

	const RequiredLabel = ({ children }: { children: React.ReactNode }) => (
		<span className="flex items-center">
			{children}
			<span className="text-red-500 ml-1">*</span>
		</span>
	);

	const ConditionalRequiredLabel = ({ children }: { children: React.ReactNode }) => {
		const packageData = useWatch({
			control: methodsForm.control,
			name: `inventories.${index}.stockMovement.product.package`,
		});

		const hasAnyPackageField = Boolean(
			(packageData?.name && packageData.name.trim().length > 0) ||
				(packageData?.barcode && packageData.barcode.trim().length > 0) ||
				(packageData?.code && packageData.code.trim().length > 0) ||
				(packageData?.quantityPerPackage !== undefined && packageData?.quantityPerPackage !== null && packageData?.quantityPerPackage > 0)
		);

		const isRequired = !isExistingIdPackage && hasAnyPackageField;

		return (
			<span className="flex items-center">
				{children}
				{isRequired && <span className="text-red-500 ml-1">*</span>}
			</span>
		);
	};

	const isAutoFilled = isExistingIdProduct || isExistingIdPackage;

	const containerClasses = isAutoFilled
		? "relative z-10 border border-blue-300 rounded-xl bg-blue-50 p-4 mb-3 transition-all hover:shadow-sm hover:shadow-blue-200/50"
		: "relative z-10 border border-gray-200 rounded-xl bg-gray-50 p-4 mb-3 transition-all hover:shadow-sm";

	return (
		<div className={containerClasses}>
			<div className="flex items-center justify-between mb-3">
				<h4 className={`text-sm font-semibold flex items-center space-x-1 ${isAutoFilled ? "text-blue-700" : "text-gray-600"}`}>
					<Box size={16} className={isAutoFilled ? "text-blue-600" : "text-gray-500"} />
					<span>Item {index + 1}</span>
					{isAutoFilled && (
						<span className="ml-2 px-2 py-0.5 text-xs bg-blue-200 text-blue-800 rounded-full font-medium">
							Preenchido automaticamente
						</span>
					)}
				</h4>
				<button
					type="button"
					onClick={removeItem}
					className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-50"
					title="Remover este item"
				>
					<Trash2 size={16} />
				</button>
			</div>
			<p className={`text-xs font-bold flex items-center mb-2 ${isAutoFilled ? "text-blue-600" : "text-gray-500"}`}>
				<Package2 size={14} className={`mr-1 ${isAutoFilled ? "text-blue-600" : ""}`} />
				Detalhes do Produto
			</p>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-2">
				<div>
					<Label className="text-xs" htmlFor={`inventories.${index}.stockMovement.product.name`}>
						<RequiredLabel>Nome do Produto</RequiredLabel>
					</Label>
					<Input
						type="text"
						placeholder="Ex: Sabão em pó"
						readOnly={isExistingIdProduct}
						className={`text-sm ${isExistingIdProduct ? readonlyClass : ""} ${
							getError(`inventories.${index}.stockMovement.product.name`) ? "border-red-500" : ""
						}`}
						{...methodsForm.register(`inventories.${index}.stockMovement.product.name`, {
							required: !isExistingIdProduct ? "Nome do produto é obrigatório" : false,
						})}
					/>
					{getError(`inventories.${index}.stockMovement.product.name`) && (
						<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.name`)?.message}</span>
					)}
				</div>

				<div>
					<Label className="text-xs flex items-center">
						<RequiredLabel>
							Código de Barras
							<div className="relative group inline-block">
								<HelpCircle className="ml-1 w-3 h-3 text-gray-400" />
								<span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 hidden w-max rounded bg-gray-700 p-1 text-xs text-white group-hover:block">
									Código de barras do produto
								</span>
							</div>
						</RequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.barcode`}
						control={methodsForm.control}
						rules={{
							required: !isExistingIdProduct ? "Código de barras é obrigatório" : false,
							pattern: {
								value: /^[0-9]*$/,
								message: "Apenas números são permitidos",
							},
						}}
						render={({ field }) => (
							<BarcodeInput
								value={field.value || ""}
								onChange={field.onChange}
								readOnly={isExistingIdProduct}
								error={getError(`inventories.${index}.stockMovement.product.barcode`)?.message}
							/>
						)}
					/>
					{getError(`inventories.${index}.stockMovement.product.barcode`) && (
						<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.barcode`)?.message}</span>
					)}
				</div>
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
				<div className="flex flex-col md:flex-row gap-2">
					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>Preço de Custo</RequiredLabel>
						</Label>{" "}
						<Controller
							name={`inventories.${index}.stockMovement.product.costPrice`}
							control={methodsForm.control}
							rules={
								!isExistingIdProduct
									? {
											required: "Preço de custo é obrigatório",
											min: {
												value: 0.01,
												message: "Preço de custo deve ser maior que zero",
											},
										}
									: undefined
							}
							render={({ field: { value, onChange } }) => {
								const numericValue = typeof value === "string" ? parseFloat(value) : value;
								return (
									<NumericFormat
										value={
											typeof numericValue === "number" && !isNaN(numericValue) && numericValue > 0 ? numericValue : undefined
										}
										onValueChange={values => {
											const newValue = values.floatValue;
											console.log("CostPrice onChange:", { newValue, type: typeof newValue });
											onChange(newValue);
										}}
										thousandSeparator="."
										decimalSeparator=","
										decimalScale={2}
										fixedDecimalScale
										prefix="R$ "
										placeholder="R$ 0,00"
										readOnly={isExistingIdProduct}
										customInput={Input}
										className={`text-sm mt-1 w-full px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-400 ${
											isExistingIdProduct ? readonlyClass : ""
										} ${getError(`inventories.${index}.stockMovement.product.costPrice`) ? "border-red-500" : "border-gray-300"}`}
									/>
								);
							}}
						/>
						{getError(`inventories.${index}.stockMovement.product.costPrice`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.costPrice`)?.message}</span>
						)}
					</div>
					<div className="w-full md:w-1/2 relative">
						<Label className="text-xs">
							<RequiredLabel>Preço de Venda</RequiredLabel>
						</Label>
						<div className="relative">
							{" "}
							<Controller
								name={`inventories.${index}.stockMovement.product.price`}
								control={methodsForm.control}
								rules={
									!isExistingIdProduct
										? {
												required: "Preço de venda é obrigatório",
												min: {
													value: 0.01,
													message: "Preço de venda deve ser maior que zero",
												},
											}
										: undefined
								}
								render={({ field: { value, onChange } }) => {
									const numericValue = typeof value === "string" ? parseFloat(value) : value;
									return (
										<NumericFormat
											value={
												typeof numericValue === "number" && !isNaN(numericValue) && numericValue > 0
													? numericValue
													: undefined
											}
											onValueChange={values => {
												const newValue = values.floatValue;
												console.log("Price onChange:", { newValue, type: typeof newValue });
												onChange(newValue);
											}}
											thousandSeparator="."
											decimalSeparator=","
											decimalScale={2}
											fixedDecimalScale
											prefix="R$ "
											placeholder="R$ 0,00"
											readOnly={isExistingIdProduct}
											customInput={Input}
											className={`text-sm mt-1 w-full px-3 py-1.5 ${
												!isExistingIdProduct && parseMonetaryValue(currentCostPrice ?? 0) > 0 ? "pr-10" : ""
											} border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-400 ${
												isExistingIdProduct ? readonlyClass : ""
											} ${getError(`inventories.${index}.stockMovement.product.price`) ? "border-red-500" : "border-gray-300"}`}
										/>
									);
								}}
							/>
							{(() => {
								const costPriceValue = parseMonetaryValue(currentCostPrice ?? 0);
								const shouldShow = !isExistingIdProduct && costPriceValue > 0;
								return (
									shouldShow && (
										<button
											type="button"
											onClick={() => setShowPricingCalculator(!showPricingCalculator)}
											className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-full transition-all duration-200 ${
												showPricingCalculator
													? "bg-mainColor text-white shadow-md"
													: "bg-gray-100 text-gray-500 hover:bg-mainColor/10 hover:text-mainColor"
											}`}
											title="Calculadora de preços"
										>
											<Calculator size={14} />
										</button>
									)
								);
							})()}
						</div>
						{getError(`inventories.${index}.stockMovement.product.price`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.price`)?.message}</span>
						)}
						{showPricingCalculator && (
							<PricingCalculator
								index={index}
								methodsForm={methodsForm}
								isOpen={showPricingCalculator}
								onClose={() => setShowPricingCalculator(false)}
							/>
						)}
					</div>
				</div>
				<div className="flex flex-col md:flex-row gap-2">
					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>Quantidade</RequiredLabel>
						</Label>
						<Controller
							name={`inventories.${index}.stockMovement.quantity`}
							control={methodsForm.control}
							rules={{
								required: "Quantidade é obrigatória",
								min: {
									value: 1,
									message: "Quantidade deve ser maior que zero",
								},
							}}
							render={({ field: { ref } }) => (
								<Input
									type="number"
									inputMode="numeric"
									pattern="[0-9]*"
									placeholder="0"
									value={calculatedQuantity}
									onChange={handleQuantityChange}
									ref={ref}
									className={`text-sm  no-spinner  ${getError(`inventories.${index}.stockMovement.quantity`) ? "border-red-500" : ""}`}
								/>
							)}
						/>
						{getError(`inventories.${index}.stockMovement.quantity`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.quantity`)?.message}</span>
						)}
					</div>
					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>Código do produto</RequiredLabel>
						</Label>
						<Input
							type="text"
							placeholder="Ex: PROD-0001"
							readOnly={isExistingIdProduct}
							className={`text-sm ${isExistingIdProduct ? readonlyClass : ""} ${
								getError(`inventories.${index}.stockMovement.product.code`) ? "border-red-500" : ""
							}`}
							{...methodsForm.register(`inventories.${index}.stockMovement.product.code`, {
								required: !isExistingIdProduct ? "Código do produto é obrigatório" : false,
							})}
						/>
						{getError(`inventories.${index}.stockMovement.product.code`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.code`)?.message}</span>
						)}
					</div>

					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>NCM</RequiredLabel>
						</Label>
						<Input
							type="text"
							placeholder="Ex: 1234.56.78"
							readOnly={isExistingIdProduct}
							className={`text-sm ${isExistingIdProduct ? readonlyClass : ""} ${
								getError(`inventories.${index}.stockMovement.product.ncm`) ? "border-red-500" : ""
							}`}
							{...methodsForm.register(`inventories.${index}.stockMovement.product.ncm`, {
								required: !isExistingIdProduct ? "NCM é obrigatório" : false,
								pattern: {
									value: /^\d{4}\.\d{2}\.\d{2}$/,
									message: "Formato inválido. Use o formato: 1234.56.78",
								},
							})}
						/>
						{getError(`inventories.${index}.stockMovement.product.ncm`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.ncm`)?.message}</span>
						)}
					</div>
				</div>
				<div className="flex flex-col md:flex-row md:items-end gap-2 mt-2">
					<div className="w-full md:w-1/2">
						<Controller
							name={`inventories.${index}.stockMovement.product.categoryId`}
							control={methodsForm.control}
							render={({ field }) => (
								<CategorySelect
									value={field.value?.toString() || null}
									onChange={value => {
										field.onChange(value ? parseInt(value) : undefined);
									}}
									label="Categoria"
									placeholder="Selecione uma categoria..."
									required={false}
									error={getError(`inventories.${index}.stockMovement.product.categoryId`)?.message}
								/>
							)}
						/>
					</div>
					<div className="w-full md:w-1/2 md:mt-2">
						<FormField
							name={`inventories.${index}.expirationDate`}
							control={methodsForm.control}
							rules={{
								required: "Data de validade é obrigatória",
							}}
							render={({ field }) => (
								<DatePickerInput
									className="w-full"
									field={field}
									inputDateClassName={`text-sm ${getError(`inventories.${index}.expirationDate`) ? "border-red-500" : ""}`}
									label="Validade"
									labelClassName="text-xs"
									disabled={false}
								/>
							)}
						/>
					</div>
				</div>
			</div>
			<Separator className={`my-4 ${isAutoFilled ? "bg-blue-200" : "bg-gray-200"}`} />
			<p className={`text-xs font-bold flex items-center mb-2 ${isAutoFilled ? "text-blue-600" : "text-gray-500"}`}>
				<Package2 size={14} className={`mr-1 ${isAutoFilled ? "text-blue-600" : ""}`} />
				Detalhes da Caixa
			</p>
			<div className="grid grid-cols-1 md:grid-cols-3 gap-2">
				<div>
					<Label className="text-xs">
						<ConditionalRequiredLabel>Nome da Caixa</ConditionalRequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.package.name`}
						control={methodsForm.control}
						render={({ field }) => (
							<Input
								type="text"
								placeholder="Ex: Caixa Sabão 12 un."
								readOnly={isExistingIdPackage}
								className={`text-sm ${isExistingIdPackage ? readonlyClass : ""} ${
									methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.name ? "border-red-500" : ""
								}`}
								{...field}
							/>
						)}
					/>
				</div>
				<div>
					<Label className="text-xs flex items-center">
						<ConditionalRequiredLabel>
							Código de Barras da Caixa
							<div className="relative group inline-block">
								<HelpCircle className="ml-1 w-3 h-3 text-gray-400" />
								<span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 hidden w-max rounded bg-gray-700 p-1 text-xs text-white group-hover:block">
									Código de barras da embalagem maior (caixa).
								</span>
							</div>
						</ConditionalRequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.package.barcode`}
						control={methodsForm.control}
						render={({ field }) => (
							<Input
								type="string"
								placeholder="Ex: 123456789012345"
								readOnly={isExistingIdPackage}
								className={`text-sm ${isExistingIdPackage ? readonlyClass : ""} ${
									methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.barcode
										? "border-red-500"
										: ""
								}`}
								{...field}
							/>
						)}
					/>
				</div>
				<div>
					<Label className="text-xs">
						<ConditionalRequiredLabel>Código da Caixa</ConditionalRequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.package.code`}
						control={methodsForm.control}
						render={({ field }) => (
							<Input
								type="text"
								placeholder="Ex: CX-0001"
								readOnly={isExistingIdPackage}
								className={`text-sm ${isExistingIdPackage ? readonlyClass : ""} ${
									methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.code ? "border-red-500" : ""
								}`}
								{...field}
							/>
						)}
					/>
				</div>
				<div>
					<Label className="text-xs">
						<ConditionalRequiredLabel>Itens por Caixa</ConditionalRequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.package.quantityPerPackage`}
						control={methodsForm.control}
						render={({ field }) => (
							<Input
								type="number"
								inputMode="numeric"
								pattern="[0-9]*"
								placeholder="Ex: 12"
								className={`text-sm no-spinner ${
									methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.quantityPerPackage
										? "border-red-500"
										: ""
								}`}
								{...field}
							/>
						)}
					/>
				</div>
			</div>
			{Object.keys(methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package || {}).length > 0 && (
				<div className="text-center mt-2">
					<span className="text-red-600 text-xs">
						{methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.name?.message ||
							"Se algum campo da caixa for preenchido, todos os campos se tornam obrigatórios"}
					</span>
				</div>
			)}
		</div>
	);
};
