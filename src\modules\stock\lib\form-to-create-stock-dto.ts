import { z } from "zod";
import { ICreateStockDto, IProductDetails, IStockMovement, ISuplierCreateStockDTO } from "../dtos/create-stock.dto";
import { createStockDtoSchema, ICreateStock } from "../validators/create-stock.validator";

export class CreateStockDtoMapper {
	public static map(form: ICreateStock): ICreateStockDto {
		return {
			invoice: CreateStockDtoMapper.mapInvoice(form.invoice),
			inventories: form.inventories.map(inv => CreateStockDtoMapper.mapInventory(inv)),
		};
	}
	private static mapInvoice(invoice: z.infer<typeof createStockDtoSchema>["invoice"]): ISuplierCreateStockDTO {
		const obj = {
			key: invoice.key,
			issueDate: invoice.issueDate,
			supplierId: invoice.supplier?.id || 0,
		};

		return CreateStockDtoMapper.removeUndefined(obj) as ISuplierCreateStockDTO;
	}

	private static mapInventory(inventory: z.infer<typeof createStockDtoSchema>["inventories"][number]): ICreateStockDto["inventories"][number] {
		const obj = {
			expirationDate: inventory.expirationDate === "" ? undefined : inventory.expirationDate,
			id: inventory.id,
			stockMovement: CreateStockDtoMapper.mapStockMovement(inventory.stockMovement),
		};

		return CreateStockDtoMapper.removeUndefined(obj) as ICreateStockDto["inventories"][number];
	}

	private static mapStockMovement(stockMovement: z.infer<typeof createStockDtoSchema>["inventories"][number]["stockMovement"]): IStockMovement {
		const obj = {
			quantity: stockMovement.quantity !== undefined ? Number(stockMovement.quantity) : undefined,
			product: CreateStockDtoMapper.mapProduct(stockMovement.product),
		};

		return CreateStockDtoMapper.removeUndefined(obj) as IStockMovement;
	}

	private static mapProduct(product: z.infer<typeof createStockDtoSchema>["inventories"][number]["stockMovement"]["product"]): IProductDetails {
		if (product.id !== undefined && product.id !== null) {
			return { id: product.id } as IProductDetails;
		}

		const convertedPrice = typeof product.price === "number" && product.price > 0 ? product.price : 0;
		const convertedCostPrice = typeof product.costPrice === "number" && product.costPrice > 0 ? product.costPrice : 0;

		// Verifica se algum campo do package foi preenchido
		const hasPackageData = Boolean(
			(product.package?.name && product.package.name.trim().length > 0) ||
				(product.package?.barcode && product.package.barcode.trim().length > 0) ||
				(product.package?.code && product.package.code.trim().length > 0) ||
				(product.package?.quantityPerPackage !== undefined &&
					product.package.quantityPerPackage !== null &&
					product.package.quantityPerPackage > 0)
		);

		const mappedPackage =
			product.package?.id !== undefined && product.package?.id !== null
				? { id: product.package.id, quantityPerPackage: product.package.quantityPerPackage }
				: hasPackageData
					? {
							name: product.package?.name || "",
							barcode: product.package?.barcode || "",
							code: product.package?.code || "",
							quantityPerPackage: product.package?.quantityPerPackage ?? 0,
						}
					: undefined;

		const obj = {
			name: product.name,
			barcode: product.barcode,
			code: product.code,
			supplierCode: product.supplierCode || "",
			price: convertedPrice,
			ncm: product.ncm,
			description: product.description,
			categoryId: product.categoryId,
			package: mappedPackage,
			costPrice: convertedCostPrice,
		};

		return CreateStockDtoMapper.removeUndefined(obj) as IProductDetails;
	}

	private static removeUndefined<T extends object>(obj: T): Partial<T> {
		return Object.fromEntries(Object.entries(obj).filter(entry => entry[1] !== undefined)) as Partial<T>;
	}
}
