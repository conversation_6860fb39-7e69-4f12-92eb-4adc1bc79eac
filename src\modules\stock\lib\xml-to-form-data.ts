import { IStockProduct, IXmlUploadItem, IXmlUploadReturnDto } from "../dtos/xml-upload-return.dto";
import { ICreateStock } from "../validators/create-stock.validator";

interface ITransformer<TInput, TOutput> {
	transform(input: TInput): TOutput;
}

export interface ITransformedProduct {
	id: number | undefined;
	name: string;
	barcode: string;
	code: string;
	price: number;
	costPrice: number;
	ncm: string;
	description: string;
	categoryId?: number;
	package: {
		name: string;
		barcode: string;
		code: string;
		quantityPerPackage: number;
		id: number | undefined;
	};
}

export interface ITransformedStockMovement {
	quantity: number;
	product: ITransformedProduct;
	packageQuantity: number | undefined;
}

export interface ITransformedInventory {
	expirationDate: string | undefined;
	stockMovement: ITransformedStockMovement;
}

export class ProductTransformer implements ITransformer<IStockProduct, ITransformedProduct> {
	transform(product: IStockProduct): ITransformedProduct {
		return {
			id: product.id,
			name: product.name,
			barcode: product.barcode,
			code: product.supplierCode ?? "",
			price: product?.price ?? 0,
			costPrice: product?.costPrice ?? 0,
			ncm: product.ncm,
			description: "",
			categoryId: undefined,
			package: {
				name: product.package?.name ?? "",
				barcode: product.package?.barcode ?? "",
				code: product.package?.code ?? "",
				quantityPerPackage: product.package?.quantityPerPackage ?? undefined,
				id: product.package?.id ?? undefined,
			},
		};
	}
}

export class StockMovementTransformer implements ITransformer<IXmlUploadItem, ITransformedStockMovement> {
	constructor(private productTransformer: ITransformer<IStockProduct, ITransformedProduct>) {}

	transform(item: IXmlUploadItem): ITransformedStockMovement {
		return {
			quantity: item.quantityToAdd ?? undefined,
			product: this.productTransformer.transform(item.product),
			packageQuantity: item.packageQuantity,
		};
	}
}

export class InventoryTransformer implements ITransformer<IXmlUploadItem, ITransformedInventory> {
	constructor(private stockMovementTransformer: ITransformer<IXmlUploadItem, ITransformedStockMovement>) {}

	transform(item: IXmlUploadItem): ITransformedInventory {
		return {
			expirationDate: undefined,
			stockMovement: this.stockMovementTransformer.transform(item),
		};
	}
}

export class XmlUploadDtoTransformer implements ITransformer<IXmlUploadReturnDto, ICreateStock> {
	constructor(private inventoryTransformer: ITransformer<IXmlUploadItem, ITransformedInventory>) {}

	transform(dto: IXmlUploadReturnDto): ICreateStock {
		return {
			invoice: dto.invoice,
			inventories: dto.items.map(item => this.inventoryTransformer.transform(item)),
		};
	}
}
