import { z } from "zod";

const optionalNumberPreprocess = z.preprocess(val => {
	if (typeof val === "string") {
		const parsed = parseFloat(val);
		return isNaN(parsed) ? undefined : parsed;
	}
	if (typeof val === "number") {
		return isNaN(val) ? undefined : val;
	}
	return val;
}, z.number().optional());

export const supplierSchema = z.object({
	id: z.number().optional(),
	name: z.string(),
});

export const invoiceSchema = z.object({
	key: z.string(),
	issueDate: z.string(),
	supplier: supplierSchema,
});

export const packageSchema = z
	.object({
		name: z.string().optional(),
		barcode: z.string().optional(),
		code: z.string().optional(),
		quantityPerPackage: z.preprocess(val => {
			if (typeof val === "string") {
				const parsed = parseFloat(val);
				return isNaN(parsed) ? undefined : parsed;
			}
			return val;
		}, z.number().optional()),
		id: z.number().optional(),
	})
	.refine(
		data => {
			if (data.id) return true;

			const hasAnyField = Boolean(
				(data.name && data.name.trim().length > 0) ||
					(data.barcode && data.barcode.trim().length > 0) ||
					(data.code && data.code.trim().length > 0) ||
					(data.quantityPerPackage !== undefined && data.quantityPerPackage !== null && data.quantityPerPackage > 0)
			);

			if (!hasAnyField) return true;

			return Boolean(
				data.name &&
					data.name.trim().length > 0 &&
					data.barcode &&
					data.barcode.trim().length > 0 &&
					data.code &&
					data.code.trim().length > 0 &&
					data.quantityPerPackage &&
					data.quantityPerPackage > 0
			);
		},
		{
			message: "Se algum campo da caixa for preenchido, todos os campos se tornam obrigatórios",
			path: ["name"],
		}
	);

const pricePreprocess = z.preprocess(
	val => {
		if (typeof val === "string") {
			const parsed = parseFloat(val);
			console.log("Price preprocess input:", { val, parsed, type: typeof parsed });
			return isNaN(parsed) ? undefined : parsed;
		}
		if (typeof val === "number") {
			return isNaN(val) ? undefined : val;
		}
		return val;
	},
	z.union([z.number().min(0, "O preço deve ser maior ou igual a zero"), z.undefined(), z.null()]).optional()
);

const productBaseObject = z
	.object({
		id: z.number().optional(),
		name: z.string(),
		barcode: z.string(),
		code: z.string(),
		supplierCode: z.string().optional(),
		price: pricePreprocess,
		costPrice: pricePreprocess,
		ncm: z.string(),
	})
	.strict();

export const productBaseSchema = productBaseObject.refine(
	data => {
		if (data.id) return true;

		// Para produtos novos, verifica se há dados suficientes para considerar como produto válido
		const hasBasicProductData = Boolean(
			(data.name && data.name.trim().length > 0) ||
				(data.barcode && data.barcode.trim().length > 0) ||
				(data.code && data.code.trim().length > 0)
		);

		// Se não há dados básicos, considera válido (produto vazio)
		if (!hasBasicProductData) return true;

		// Se há dados básicos, os preços devem ser válidos
		const hasCostPrice = typeof data.costPrice === "number" && data.costPrice > 0;
		const hasPrice = typeof data.price === "number" && data.price > 0;

		return hasCostPrice && hasPrice;
	},
	{
		message: "Preços são obrigatórios e devem ser maiores que zero para produtos novos",
		path: ["price"],
	}
);

export const productDetailsSchema = productBaseObject
	.extend({
		description: z.string(),
		categoryId: z.number().optional(),
		package: packageSchema,
	})
	.refine(
		data => {
			if (data.id) return true;

			// Para produtos novos, verifica se há dados suficientes para considerar como produto válido
			const hasBasicProductData = Boolean(
				(data.name && data.name.trim().length > 0) ||
					(data.barcode && data.barcode.trim().length > 0) ||
					(data.code && data.code.trim().length > 0)
			);

			// Se não há dados básicos, considera válido (produto vazio)
			if (!hasBasicProductData) return true;

			// Se há dados básicos, os preços devem ser válidos
			const hasCostPrice = typeof data.costPrice === "number" && data.costPrice > 0;
			const hasPrice = typeof data.price === "number" && data.price > 0;

			return hasCostPrice && hasPrice;
		},
		{
			message: "Preços são obrigatórios e devem ser maiores que zero para produtos novos",
			path: ["price"],
		}
	);

export const stockMovementSchema = z
	.object({
		description: z.string().optional(),
		quantity: optionalNumberPreprocess,
		product: productDetailsSchema,
		packageQuantity: optionalNumberPreprocess,
	})
	.refine(
		data => {
			// Se o produto tem ID, não precisa validar outros campos
			if (data.product.id) return true;

			// Verifica se há dados suficientes para considerar como um item válido
			const hasProductData = Boolean(
				(data.product.name && data.product.name.trim().length > 0) ||
					(data.product.barcode && data.product.barcode.trim().length > 0) ||
					(data.product.code && data.product.code.trim().length > 0)
			);

			// Se não há dados do produto, considera válido (item vazio)
			if (!hasProductData) return true;

			// Se há dados do produto, a quantidade deve ser válida
			return typeof data.quantity === "number" && data.quantity > 0;
		},
		{
			message: "Quantidade é obrigatória quando há dados do produto",
			path: ["quantity"],
		}
	);

export const inventorySchema = z.object({
	id: z.number().optional(),
	expirationDate: z.string().optional(),
	stockMovement: stockMovementSchema,
});

export const createStockDtoSchema = z.object({
	invoice: invoiceSchema,
	inventories: z.array(inventorySchema),
});

export type ICreateStock = z.infer<typeof createStockDtoSchema>;
