import { z } from "zod";

const optionalNumberPreprocess = z.preprocess(val => {
	if (typeof val === "string") {
		const parsed = parseFloat(val);
		return isNaN(parsed) ? undefined : parsed;
	}
	if (typeof val === "number") {
		return isNaN(val) ? undefined : val;
	}
	return val;
}, z.number().optional());

export const supplierSchema = z.object({
	id: z.number().optional(),
	name: z.string(),
});

export const invoiceSchema = z.object({
	key: z.string(),
	issueDate: z.string(),
	supplier: supplierSchema,
});

export const packageSchema = z
	.object({
		name: z.string().optional(),
		barcode: z.string().optional(),
		code: z.string().optional(),
		quantityPerPackage: z.preprocess(val => {
			if (typeof val === "string") {
				const parsed = parseFloat(val);
				return isNaN(parsed) ? undefined : parsed;
			}
			return val;
		}, z.number().optional()),
		id: z.number().optional(),
	})
	.refine(
		data => {
			if (data.id) return true;

			const hasAnyField = Boolean(
				data.name || data.barcode || data.code || (data.quantityPerPackage !== undefined && data.quantityPerPackage !== null)
			);

			if (!hasAnyField) return true;

			return Boolean(
				data.name &&
					data.name.trim().length > 0 &&
					data.barcode &&
					data.barcode.trim().length > 0 &&
					data.code &&
					data.code.trim().length > 0 &&
					data.quantityPerPackage &&
					data.quantityPerPackage > 0
			);
		},
		{
			message: "Se algum campo da caixa for preenchido, todos os campos se tornam obrigatórios",
			path: ["name"],
		}
	);

const pricePreprocess = z.preprocess(
	val => {
		if (typeof val === "string") {
			const parsed = parseFloat(val);
			console.log("Price preprocess input:", { val, parsed, type: typeof parsed });
			return isNaN(parsed) ? undefined : parsed;
		}
		if (typeof val === "number") {
			return isNaN(val) ? undefined : val;
		}
		return val;
	},
	z.union([z.number().min(0, "O preço deve ser maior ou igual a zero"), z.undefined(), z.null()]).optional()
);

const productBaseObject = z.object({
	id: z.number().optional(),
	name: z.string(),
	barcode: z.string(),
	code: z.string(),
	price: pricePreprocess,
	costPrice: pricePreprocess,
	ncm: z.string(),
});

export const productBaseSchema = productBaseObject.refine(
	data => {
		if (data.id) return true;
		return typeof data.price === "number" && typeof data.costPrice === "number" && data.price > 0 && data.costPrice > 0;
	},
	{
		message: "Preços são obrigatórios e devem ser maiores que zero para produtos novos",
		path: ["price"],
	}
);

export const productDetailsSchema = productBaseObject
	.extend({
		description: z.string(),
		categoryId: z.number().optional(),
		package: packageSchema,
	})
	.refine(
		data => {
			if (data.id) return true;
			return typeof data.price === "number" && typeof data.costPrice === "number" && data.price > 0 && data.costPrice > 0;
		},
		{
			message: "Preços são obrigatórios e devem ser maiores que zero para produtos novos",
			path: ["price"],
		}
	);

export const stockMovementSchema = z.object({
	description: z.string().optional(),
	quantity: optionalNumberPreprocess,
	product: productDetailsSchema,
	packageQuantity: optionalNumberPreprocess,
});

export const inventorySchema = z.object({
	id: z.number().optional(),
	expirationDate: z.string().optional(),
	stockMovement: stockMovementSchema,
});

export const createStockDtoSchema = z.object({
	invoice: invoiceSchema,
	inventories: z.array(inventorySchema),
});

export type ICreateStock = z.infer<typeof createStockDtoSchema>;
